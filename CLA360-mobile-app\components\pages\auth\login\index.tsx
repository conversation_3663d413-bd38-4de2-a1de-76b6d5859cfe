import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    StatusBar,
    Animated,
    TouchableWithoutFeedback,
    Keyboard,
    Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { images } from "@/constants";
import { Ionicons } from '@expo/vector-icons';
import { useSignIn } from '@clerk/clerk-expo';
import { Link, router } from 'expo-router';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

const SignInComponent = () => {
    const { signIn, setActive, isLoaded } = useSignIn();

    const [state, setState] = useState({
        form: {
            email: "",
            password: "",
        },
        showPassword: false,
        focusedField: null,
        fadeAnim: new Animated.Value(0),
        slideAnim: new Animated.Value(50),
    });

    const updateState = (updates: any) => {
        setState(prevState => ({ ...prevState, ...updates }));
    };

    const updateForm = (field: string, value: string) => {
        setState(prevState => ({
            ...prevState,
            form: { ...prevState.form, [field]: value }
        }));
    };

    useEffect(() => {
        Animated.parallel([
            Animated.timing(state.fadeAnim, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
            }),
            Animated.timing(state.slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const onSignInPress = React.useCallback(async () => {
        if (!isLoaded) return;

        // Basic validation
        if (!state.form.email.trim()) {
            Alert.alert("Error", "Please enter your email");
            return;
        }
        if (!state.form.password.trim()) {
            Alert.alert("Error", "Please enter your password");
            return;
        }

        try {
            const signInAttempt = await signIn.create({
                identifier: state.form.email,
                password: state.form.password,
            });

            if (signInAttempt.status === 'complete') {
                await setActive({ session: signInAttempt.createdSessionId });
                router.replace('/');
            } else {
                console.error(JSON.stringify(signInAttempt, null, 2));
                Alert.alert("Error", "Sign in failed. Please try again.");
            }
        } catch (err: any) {
            console.error(JSON.stringify(err, null, 2));
            Alert.alert("Error", err.errors?.[0]?.longMessage || "An error occurred during sign in");
        }
    }, [isLoaded, state.form.email, state.form.password]);

    const CustomInput = ({
        label,
        placeholder,
        icon,
        value,
        onChangeText,
        secureTextEntry = false,
        keyboardType = "default",
        showPasswordToggle = false,
        isPasswordVisible = false,
        onTogglePassword,
        fieldKey
    }: {
        label: string;
        placeholder: string;
        icon: any;
        value: string;
        onChangeText: (value: string) => void;
        secureTextEntry?: boolean;
        keyboardType?: "default" | "email-address" | "numeric" | "phone-pad";
        showPasswordToggle?: boolean;
        isPasswordVisible?: boolean;
        onTogglePassword?: () => void;
        fieldKey: string;
    }) => {
        const isFocused = state.focusedField === fieldKey;

        return (
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>{label}</Text>
                <View style={[styles.inputWrapper, isFocused && styles.inputWrapperFocused]}>
                    <View style={styles.inputIconContainer}>
                        <Ionicons
                            name={icon}
                            size={20}
                            color={isFocused ? "#6366f1" : "#9ca3af"}
                        />
                    </View>
                    <TextInput
                        style={styles.textInput}
                        placeholder={placeholder}
                        placeholderTextColor="#9ca3af"
                        value={value}
                        onChangeText={onChangeText}
                        secureTextEntry={secureTextEntry && !isPasswordVisible}
                        keyboardType={keyboardType}
                        // onFocus={() => updateState({ focusedField: fieldKey })}
                        // onBlur={() => updateState({ focusedField: null })}
                        autoCorrect={false}
                        autoCapitalize={keyboardType === "email-address" ? "none" : "words"}
                        returnKeyType="next"
                    />
                    {showPasswordToggle && (
                        <TouchableOpacity
                            onPress={onTogglePassword}
                            style={styles.passwordToggle}
                        >
                            <Ionicons
                                name={isPasswordVisible ? "eye-off-outline" : "eye-outline"}
                                size={20}
                                color="#9ca3af"
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StatusBar barStyle="light-content" backgroundColor="#6366f1" />
            <LinearGradient
                colors={['#f8fafc', '#e2e8f0']}
                style={styles.backgroundGradient}
            >
                <TouchableWithoutFeedback onPress={Keyboard.dismiss} style={styles.touchableContainer}>
                    <KeyboardAwareScrollView
                        style={styles.scrollView}
                        contentContainerStyle={styles.scrollContent}
                        enableOnAndroid={true}
                        extraScrollHeight={20}
                        keyboardShouldPersistTaps="handled"
                    >
                        <Animated.View
                            style={[
                                styles.content,
                                {
                                    opacity: state.fadeAnim,
                                    transform: [{ translateY: state.slideAnim }],
                                },
                            ]}
                        >
                            {/* Header */}
                            <View style={styles.header}>
                                <View style={styles.logoContainer}>
                                    <LinearGradient
                                        colors={['#ffffff', '#4287f5']}
                                    style={styles.logo}
                                    >
                                        <Image
                                            source={images.logo}
                                            style={{ width: 40, height: 40 }}
                                            resizeMode="contain"
                                        />
                                    </LinearGradient>
                                </View>
                                <Text style={styles.title}>Welcome Back</Text>
                                <Text style={styles.subtitle}>Sign in to continue your journey</Text>
                            </View>

                            {/* Form Card */}
                            <BlurView intensity={20} tint="light" style={styles.formCard}>
                                <CustomInput
                                    label="Email Address"
                                    placeholder="Enter your email"
                                    icon="mail-outline"
                                    value={state.form.email}
                                    onChangeText={(value) => updateForm('email', value)}
                                    keyboardType="email-address"
                                    fieldKey="email"
                                />

                                <CustomInput
                                    label="Password"
                                    placeholder="Enter your password"
                                    icon="lock-closed-outline"
                                    value={state.form.password}
                                    onChangeText={(value) => updateForm('password', value)}
                                    secureTextEntry={true}
                                    showPasswordToggle={true}
                                    isPasswordVisible={state.showPassword}
                                    onTogglePassword={() => updateState({ showPassword: !state.showPassword })}
                                    fieldKey="password"
                                />

                                {/* Sign In Button */}
                                <TouchableOpacity
                                    onPress={onSignInPress}
                                    activeOpacity={0.9}
                                    style={styles.signInButton}
                                >
                                    <LinearGradient
                                        colors={['#6366f1', '#8b5cf6']}
                                        style={styles.signInGradient}
                                    >
                                        <Text style={styles.signInButtonText}>Sign In</Text>
                                        <Ionicons name="arrow-forward" size={18} color="white" />
                                    </LinearGradient>
                                </TouchableOpacity>

                                {/* Sign Up Link */}
                                <View style={styles.signUpContainer}>
                                    <Text style={styles.signUpText}>Don't have an account? </Text>
                                    <Link href="/sign-up" asChild>
                                        <TouchableOpacity>
                                            <Text style={styles.signUpLink}>Sign Up</Text>
                                        </TouchableOpacity>
                                    </Link>
                                </View>
                            </BlurView>
                        </Animated.View>
                    </KeyboardAwareScrollView>
                </TouchableWithoutFeedback>
            </LinearGradient>
        </View>
    );
};

const styles = {
    container: {
        flex: 1,
    },
    backgroundGradient: {
        flex: 1,
    },
    touchableContainer: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
        paddingBottom: 40,
    },
    content: {
        flex: 1,
        paddingHorizontal: 24,
        paddingTop: 60,
    },
    header: {
        alignItems: 'center' as const,
        marginBottom: 40,
    },
    logoContainer: {
        marginBottom: 20,
    },
    logo: {
        width: 64,
        height: 64,
        borderRadius: 16,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    title: {
        fontSize: 32,
        fontWeight: 'bold' as const,
        color: '#1f2937',
        marginBottom: 8,
        textAlign: 'center' as const,
    },
    subtitle: {
        fontSize: 16,
        color: '#6b7280',
        textAlign: 'center' as const,
        lineHeight: 22,
    },
    formCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 20,
        padding: 24,
        marginHorizontal: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 20,
        elevation: 12,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    inputContainer: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '600' as const,
        color: '#374151',
        marginBottom: 8,
        marginLeft: 4,
    },
    inputWrapper: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        backgroundColor: '#f9fafb',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        paddingHorizontal: 16,
        // paddingVertical: 4,
        height: 52,
    },
    inputWrapperFocused: {
        borderColor: '#6366f1',
        backgroundColor: '#ffffff',
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    inputIconContainer: {
        marginRight: 12,
    },
    textInput: {
        flex: 1,
        fontSize: 16,
        color: '#1f2937',
        // paddingVertical: 12,
    },
    passwordToggle: {
        padding: 8,
        marginLeft: 8,
    },
    signInButton: {
        marginTop: 32,
        marginBottom: 24,
        borderRadius: 16,
        overflow: 'hidden' as const,
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 8,
    },
    signInGradient: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        paddingVertical: 16,
        paddingHorizontal: 32,
        gap: 8,
    },
    signInButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600' as const,
    },
    signUpContainer: {
        flexDirection: 'row' as const,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        marginTop: 8,
    },
    signUpText: {
        fontSize: 14,
        color: '#6b7280',
    },
    signUpLink: {
        fontSize: 14,
        color: '#6366f1',
        fontWeight: '600' as const,
    },
};

export default SignInComponent;
