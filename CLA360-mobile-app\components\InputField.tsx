import {
  View,
  Text,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Image,
  TextInput,
  Platform,
  Keyboard,
  StyleSheet,
  Pressable,
} from "react-native";
import { InputFieldProps } from "@/types/type";
import React, { useRef, useState } from "react";
import PhoneInput from "react-native-phone-number-input";
import Radio from "./radio";
import { Picker } from "@react-native-picker/picker";
import DateTimePicker from "@react-native-community/datetimepicker";
import RNDateTimePicker from "@react-native-community/datetimepicker";
// import 'react-phone-number-input/style.css'

const InputField = ({
  label,
  labelStyle,
  icon,
  secureTextEntry = false,
  containerStyle,
  inputStyle,
  iconStyle,
  className,
  placeholder,
  items,
  itemSelect,
  required,
  type = "text",
  options = [],
  selectedValue,
  ...props
}: InputFieldProps) => {
  const phoneInputRef = useRef<PhoneInput>(null);


  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className={`my-2 w-full ${containerStyle}`}>
        <Text className={`text-lg font-JakartaSemiBold mb-3 ${labelStyle}`}>
          {label}{required ? <Text className="text-red-700">*</Text> : ""}
        </Text>
        <View
          className={`flex flex-row justify-start items-center relative rounded-md bg-neutral-200 border border-neutral-100 focus:border-primary-500 ${containerStyle} mt-3`}
        >
          {icon && (
            <Image source={icon} className={`w-6 h-6 ml-4 ${iconStyle}`} />
          )}
          {type == "textarea" ? (
            <TextInput
              className={`rounded-full p-4 font-JakartaSemiBold text-[15px] flex-1 ${inputStyle} text-left text-gray-500`}
              placeholder={placeholder}
              placeholderTextColor="#88909e"
              multiline
              numberOfLines={4}
              {...props}
            />
          ) : type === "phone" ? (
            <PhoneInput
              ref={phoneInputRef}
              defaultCode="US"
              layout="first"
              placeholder={placeholder}
              {...props}
            />
          ) :  (
            <TextInput
              className={`rounded-full p-4 font-JakartaSemiBold text-[15px] flex-1 ${inputStyle} text-left text-gray-500`}
              placeholder={placeholder}
              placeholderTextColor="#88909e"
              secureTextEntry={secureTextEntry}
              {...props}
            />
          )}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default InputField;

const styles = StyleSheet.create({
  keyboardAvoid: {
    flex: 1,
  },
  container: {
    marginVertical: 8,
    width: "100%",
  },
  label: {
    fontSize: 18,
    fontFamily: "JakartaSemiBold",
    marginBottom: 6,
    color: "#333",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 50,
    borderWidth: 1,
    borderColor: "#f5f5f5",
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 6,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontFamily: "JakartaSemiBold",
    fontSize: 15,
    color: "#555",
  },
  textarea: {
    textAlignVertical: "top",
    paddingTop: 8,
  },
  picker: {
    flex: 1,
    color: "#555",
  },
  phoneInputContainer: {
    backgroundColor: "transparent",
    borderWidth: 0,
  },
  phoneInputTextContainer: {
    backgroundColor: "#f5f5f5",
    borderRadius: 50,
  },
});
