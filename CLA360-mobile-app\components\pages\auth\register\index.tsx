import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    Alert,
    StatusBar,
    Dimensions,
    Animated,
    Platform,
    KeyboardAvoidingView,
    Keyboard,
    TouchableWithoutFeedback,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import ReactNativeModal from 'react-native-modal';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { icons, images } from "@/constants";

import { Ionicons } from '@expo/vector-icons';
import { useSignUp } from '@clerk/clerk-expo';
import { Link, router } from 'expo-router';
import { Image } from "react-native";
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

const { width, height } = Dimensions.get('window');

const ModernSignUp = () => {
    const { isLoaded, signUp, setActive } = useSignUp();

    const [state, setState] = useState({
        form: {
            fullName: "",
            applicationType: "",
            email: "",
            password: "",
            confirmPassword: "",
        },
        showPassword: false,
        showConfirmPassword: false,
        showSuccessModal: false,
        focusedField: null,
        verification: {
            state: "default",
            error: "",
            code: "",
        },
        fadeAnim: new Animated.Value(0),
        slideAnim: new Animated.Value(50),
    });

    const applicationTypes = [
        { label: "🏃‍♀️ Athlete", value: "Athlete" },
        { label: "🎓 Masters Student", value: "Masters" },
        { label: "🔬 PhD Candidate", value: "PHD" },
        { label: "📚 Undergraduate", value: "Degree" }
    ];

    const updateState = (updates: any) => {
        setState(prevState => ({ ...prevState, ...updates }));
    };

    const updateForm = (field: any, value: any) => {
        setState(prevState => ({
            ...prevState,
            form: { ...prevState.form, [field]: value }
        }));
    };

    const updateVerification = (updates: any) => {
        setState(prevState => ({
            ...prevState,
            verification: { ...prevState.verification, ...updates }
        }));
    };

    useEffect(() => {
        Animated.parallel([
            Animated.timing(state.fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(state.slideAnim, {
                toValue: 0,
                duration: 600,
                useNativeDriver: true,
            }),
        ]).start();

        // Add keyboard event listeners to prevent unexpected dismissal
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            // Keyboard is shown, ensure it stays open
        });

        const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
            // Keyboard is hidden
        });

        return () => {
            keyboardDidShowListener?.remove();
            keyboardDidHideListener?.remove();
        };
    }, []);

    const onSignUpPress = async () => {
        if (!isLoaded) return;

        // Basic validation
        if (!state.form.fullName.trim()) {
            Alert.alert("Error", "Please enter your full name");
            return;
        }
        if (!state.form.email.trim()) {
            Alert.alert("Error", "Please enter your email");
            return;
        }
        if (!state.form.password.trim()) {
            Alert.alert("Error", "Please enter a password");
            return;
        }
        if (state.form.password !== state.form.confirmPassword) {
            Alert.alert("Error", "Passwords don't match");
            return;
        }

        try {
            await signUp.create({
                emailAddress: state.form.email,
                password: state.form.password,
            });

            await signUp.prepareEmailAddressVerification({ strategy: "email_code" });
            updateVerification({ state: "pending", error: "" });
        } catch (err: any) {
            Alert.alert("Error", err.errors?.[0]?.longMessage || "An error occurred");
        }
    };

    const onPressVerify = async () => {
        if (!isLoaded) return;

        try {
            const completeSignUp = await signUp.attemptEmailAddressVerification({
                code: state.verification.code,
            });

            if (completeSignUp.status === "complete") {
                await setActive({ session: completeSignUp.createdSessionId });
                updateVerification({ state: "success" });
                updateState({ showSuccessModal: true });
            } else {
                updateVerification({ state: "failed", error: "Verification Failed" });
            }
        } catch (err: any) {
            updateVerification({
                state: "pending",
                error: err.errors?.[0]?.longMessage || "Verification failed",
            });
        }
    };

    const CustomInput = ({
        label,
        placeholder,
        icon,
        value,
        onChangeText,
        secureTextEntry = false,
        keyboardType = "default",
        showPasswordToggle = false,
        isPasswordVisible = false,
        onTogglePassword,
        fieldKey
    }: {
        label: string;
        placeholder: string;
        icon: any;
        value: string;
        onChangeText: (value: string) => void;
        secureTextEntry?: boolean;
        keyboardType?: "default" | "email-address" | "numeric" | "phone-pad";
        showPasswordToggle?: boolean;
        isPasswordVisible?: boolean;
        onTogglePassword?: () => void;
        fieldKey: string;
    }) => {
        const isFocused = state.focusedField === fieldKey;

        return (
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>{label}</Text>
                <View style={[styles.inputWrapper, isFocused && styles.inputWrapperFocused]}>
                    <View style={styles.inputIconContainer}>
                        <Ionicons
                            name={icon}
                            size={20}
                            color={isFocused ? "#6366f1" : "#9ca3af"}
                        />
                    </View>
                    <TextInput
                        style={styles.textInput}
                        placeholder={placeholder}
                        placeholderTextColor="#9ca3af"
                        value={value}
                        onChangeText={onChangeText}
                        secureTextEntry={secureTextEntry && !isPasswordVisible}
                        // keyboardType={keyboardType}

                        // autoCapitalize={keyboardType === "email-address" ? "none" : "words"}
                        // returnKeyType="next"
                        // enablesReturnKeyAutomatically={false}
                        // clearButtonMode="while-editing"
                        // textContentType={
                        //     fieldKey === "email" ? "emailAddress" :
                        //     fieldKey === "password" ? "newPassword" :
                        //     fieldKey === "confirmPassword" ? "newPassword" :
                        //     fieldKey === "fullName" ? "name" : "none"
                        // }
                    />
                    {showPasswordToggle && (
                        <TouchableOpacity
                            style={styles.passwordToggle}
                            onPress={onTogglePassword}
                            activeOpacity={0.7}
                        >
                            <Ionicons
                                name={isPasswordVisible ? "eye-off-outline" : "eye-outline"}
                                size={20}
                                color="#9ca3af"
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    };

    const CustomPicker = ({ label, value, onValueChange, options }: {
        label: string;
        value: string;
        onValueChange: (value: string) => void;
        options: Array<{ label: string; value: string }>;
    }) => (
        <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{label}</Text>
            <View style={styles.pickerWrapper}>
                <View style={styles.inputIconContainer}>
                    <Ionicons name="school-outline" size={20} color="#9ca3af" />
                </View>
                <Picker
                    selectedValue={value}
                    onValueChange={onValueChange}
                    style={styles.picker}
                    dropdownIconColor="#6366f1"
                >
                    <Picker.Item label="Select your path..." value="" />
                    {options.map((option, index) => (
                        <Picker.Item
                            label={option.label}
                            value={option.value}
                            key={index}
                        />
                    ))}
                </Picker>
            </View>
        </View>
    );

    // Function to dismiss keyboard when tapping outside inputs
    const dismissKeyboard = () => {
        Keyboard.dismiss();
    };

    return (
        <View style={styles.container}>
            <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

            <LinearGradient
                colors={['#f8fafc', '#e2e8f0']}
                style={styles.backgroundGradient}
            >
                <KeyboardAwareScrollView
                    style={styles.scrollView}
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="always"
                    enableOnAndroid={true}
                    enableAutomaticScroll={true}
                    extraScrollHeight={150}
                    keyboardOpeningTime={250}
                    resetScrollToCoords={{ x: 0, y: 0 }}
                    scrollEnabled={true}
                    nestedScrollEnabled={true}
                    keyboardDismissMode="none"
                >
                    <Animated.View
                        style={[
                            styles.content,
                            {
                                opacity: state.fadeAnim,
                                transform: [{ translateY: state.slideAnim }],
                            },
                        ]}
                    >
                        {/* Header */}
                        <View style={styles.header}>
                            <View style={styles.logoContainer}>
                                <LinearGradient
                                    colors={['#ffffff', '#4287f5']}
                                    style={styles.logo}
                                >
                                    {/* <Ionicons name="rocket-outline" size={28} color="white" /> */}
                                    <Image
                                        source={images.logo}
                                        className="z-0 w-full h-[50px]"
                                        resizeMode="contain"
                                    />
                                </LinearGradient>
                            </View>
                            <Text style={styles.title}>Create Account</Text>
                            <Text style={styles.subtitle}>Join thousands of learners on their journey</Text>
                        </View>

                        {/* Form Card */}
                        <View style={styles.formCard}>
                            <CustomInput
                                label="Full Name"
                                placeholder="Enter your full name"
                                icon="person-outline"
                                value={state.form.fullName}
                                onChangeText={(value) => updateForm('fullName', value)}
                                fieldKey="fullName"
                            />

                            <CustomInput
                                label="Email Address"
                                placeholder="Enter your email"
                                icon="mail-outline"
                                value={state.form.email}
                                onChangeText={(value) => updateForm('email', value)}
                                keyboardType="email-address"
                                fieldKey="email"
                            />

                            <CustomPicker
                                label="Application Type"
                                value={state.form.applicationType}
                                onValueChange={(value) => updateForm('applicationType', value)}
                                options={applicationTypes}
                            />

                            <CustomInput
                                label="Password"
                                placeholder="Create a strong password"
                                icon="lock-closed-outline"
                                value={state.form.password}
                                onChangeText={(value) => updateForm('password', value)}
                                secureTextEntry={true}
                                showPasswordToggle={true}
                                isPasswordVisible={state.showPassword}
                                onTogglePassword={() => updateState({ showPassword: !state.showPassword })}
                                fieldKey="password"
                            />

                            <CustomInput
                                label="Confirm Password"
                                placeholder="Confirm your password"
                                icon="lock-closed-outline"
                                value={state.form.confirmPassword}
                                onChangeText={(value) => updateForm('confirmPassword', value)}
                                secureTextEntry={true}
                                showPasswordToggle={true}
                                isPasswordVisible={state.showConfirmPassword}
                                onTogglePassword={() => updateState({ showConfirmPassword: !state.showConfirmPassword })}
                                fieldKey="confirmPassword"
                            />

                            {/* Sign Up Button */}
                            <TouchableOpacity
                                onPress={onSignUpPress}
                                activeOpacity={0.9}
                                style={styles.signUpButton}
                            >
                                <LinearGradient
                                    colors={['#6366f1', '#8b5cf6']}
                                    style={styles.signUpGradient}
                                >
                                    <Text style={styles.signUpButtonText}>Create Account</Text>
                                    <Ionicons name="arrow-forward" size={18} color="white" />
                                </LinearGradient>
                            </TouchableOpacity>

                            {/* Sign In Link */}
                            <View style={styles.signInContainer}>
                                <Text style={styles.signInText}>Already have an account? </Text>
                                <Link href="/sign-in">
                                    <Text style={styles.signInLink}>Sign In</Text>
                                </Link>
                            </View>
                        </View>
                    </Animated.View>
                </KeyboardAwareScrollView>

                {/* Verification Modal */}
                <ReactNativeModal
                    isVisible={state.verification.state === "pending"}
                    backdropColor="rgba(0, 0, 0, 0.5)"
                    backdropOpacity={1}
                    animationIn="slideInUp"
                    animationOut="slideOutDown"
                    onModalHide={() => {
                        if (state.verification.state === "success") {
                            Alert.alert("Success", "Welcome! Your account has been created.");
                            router.push("/(root)/(tabs)/home");
                        }
                    }}
                >
                    <View style={styles.modalContainer}>
                        <View style={styles.modalContent}>
                            <View style={styles.modalHeader}>
                                <View style={styles.modalIconContainer}>
                                    <Ionicons name="mail-outline" size={32} color="#6366f1" />
                                </View>
                                <Text style={styles.modalTitle}>Verify Your Email</Text>
                                <Text style={styles.modalSubtitle}>
                                    We've sent a verification code to{'\n'}{state.form.email}
                                </Text>
                            </View>

                            <View style={styles.codeInputContainer}>
                                <Text style={styles.codeLabel}>Enter verification code</Text>
                                <TextInput
                                    style={styles.codeInput}
                                    placeholder="000000"
                                    placeholderTextColor="#9ca3af"
                                    value={state.verification.code}
                                    onChangeText={(code) => updateVerification({ code })}
                                    keyboardType="numeric"
                                    maxLength={6}
                                />
                            </View>

                            {state.verification.error && (
                                <Text style={styles.errorText}>{state.verification.error}</Text>
                            )}

                            <TouchableOpacity
                                onPress={onPressVerify}
                                activeOpacity={0.9}
                                style={styles.verifyButton}
                            >
                                <Text style={styles.verifyButtonText}>Verify & Continue</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ReactNativeModal>

                {/* Success Modal */}
                <ReactNativeModal
                    isVisible={state.showSuccessModal}
                    backdropColor="rgba(0, 0, 0, 0.5)"
                    animationIn="zoomIn"
                    animationOut="zoomOut"
                    onModalHide={() => updateState({ showSuccessModal: false })}
                >
                    <View style={styles.successModal}>
                        <View style={styles.successContent}>
                            <View style={styles.successIconContainer}>
                                <Ionicons name="checkmark-circle" size={64} color="#10b981" />
                            </View>
                            <Text style={styles.successTitle}>Welcome!</Text>
                            <Text style={styles.successSubtitle}>
                                Your account has been created successfully.{'\n'}Let's get started!
                            </Text>
                        </View>
                    </View>
                </ReactNativeModal>
            </LinearGradient >
        </View >
    );
};

const styles = {
    container: {
        flex: 1,
    },
    backgroundGradient: {
        flex: 1,
    },
    touchableContainer: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
        paddingBottom: 40,
    },
    content: {
        flex: 1,
        paddingHorizontal: 24,
        paddingTop: 60,
    },
    header: {
        alignItems: 'center' as const,
        marginBottom: 40,
    },
    logoContainer: {
        marginBottom: 20,
    },
    logo: {
        width: 64,
        height: 64,
        borderRadius: 16,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    title: {
        fontSize: 32,
        fontWeight: 'bold' as const,
        color: '#1f2937',
        marginBottom: 8,
        textAlign: 'center' as const,
    },
    subtitle: {
        fontSize: 16,
        color: '#6b7280',
        textAlign: 'center' as const,
        lineHeight: 22,
    },
    formCard: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 24,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    inputContainer: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '600' as const,
        color: '#374151',
        marginBottom: 8,
        marginLeft: 2,
    },
    inputWrapper: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        backgroundColor: '#f9fafb',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        paddingHorizontal: 16,
        height: 52,
    },
    inputWrapperFocused: {
        borderColor: '#6366f1',
        backgroundColor: '#ffffff',
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    inputIconContainer: {
        marginRight: 12,
    },
    textInput: {
        flex: 1,
        fontSize: 16,
        color: '#1f2937',
        fontWeight: '500' as const,
    },
    passwordToggle: {
        padding: 4,
    },
    pickerWrapper: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        backgroundColor: '#f9fafb',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        paddingLeft: 16,
        height: 52,
    },
    picker: {
        flex: 1,
        height: 52,
        color: '#1f2937',
    },
    signUpButton: {
        marginTop: 12,
        marginBottom: 24,
        borderRadius: 12,
        overflow: 'hidden' as const,
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
    },
    signUpGradient: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        paddingVertical: 16,
        paddingHorizontal: 24,
    },
    signUpButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600' as const,
        marginRight: 8,
    },
    signInContainer: {
        flexDirection: 'row' as const,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
    },
    signInText: {
        color: '#6b7280',
        fontSize: 14,
        fontWeight: '500' as const,
    },
    signInLink: {
        color: '#6366f1',
        fontSize: 14,
        fontWeight: '600' as const,
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        margin: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 12,
        elevation: 12,
    },
    modalContent: {
        padding: 32,
    },
    modalHeader: {
        alignItems: 'center' as const,
        marginBottom: 32,
    },
    modalIconContainer: {
        backgroundColor: '#f0f4ff',
        padding: 16,
        borderRadius: 50,
        marginBottom: 16,
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold' as const,
        color: '#1f2937',
        marginBottom: 8,
        textAlign: 'center' as const,
    },
    modalSubtitle: {
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center' as const,
        lineHeight: 20,
    },
    codeInputContainer: {
        marginBottom: 24,
    },
    codeLabel: {
        fontSize: 14,
        fontWeight: '600' as const,
        color: '#374151',
        textAlign: 'center' as const,
        marginBottom: 12,
    },
    codeInput: {
        backgroundColor: '#f9fafb',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        paddingVertical: 16,
        paddingHorizontal: 20,
        fontSize: 18,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
        color: '#1f2937',
        letterSpacing: 4,
    },
    errorText: {
        color: '#ef4444',
        fontSize: 14,
        textAlign: 'center' as const,
        marginBottom: 20,
        fontWeight: '500' as const,
    },
    verifyButton: {
        backgroundColor: '#6366f1',
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: 'center' as const,
        shadowColor: '#6366f1',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
    },
    verifyButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600' as const,
    },
    successModal: {
        backgroundColor: 'white',
        borderRadius: 20,
        margin: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 12,
        elevation: 12,
    },
    successContent: {
        padding: 40,
        alignItems: 'center' as const,
    },
    successIconContainer: {
        marginBottom: 20,
    },
    successTitle: {
        fontSize: 24,
        fontWeight: 'bold' as const,
        color: '#1f2937',
        marginBottom: 12,
        textAlign: 'center' as const,
    },
    successSubtitle: {
        fontSize: 16,
        color: '#6b7280',
        textAlign: 'center' as const,
        lineHeight: 22,
    },
};

export default ModernSignUp;
