import {TextInputProps, TouchableOpacityProps} from "react-native";

declare interface University {
    name: string,
    abbr: string,
    location: string,
    country: string,
    website: string,
    email: string,
    compatibility: number,
    logo?: any,
}
declare interface Transcript {
    id: string,
    type: string,
    name: string,
    year: string,
    courses?: number,
}
declare interface PaymentProps {
    fullName: string;
    email: string;
    amount: string;
    driverId: number;
    rideTime: number;
  }
declare interface Applications {
    name: string,
    abbr: string,
    location: string,
    country: string,
    website: string,
    email: string,
    courses: {},
    logo?: any,
}



declare interface ButtonProps extends TouchableOpacityProps {
    title: string;
    bgVariant?: "primary" | "secondary" | "danger" | "outline" | "success";
    textVariant?: "primary" | "default" | "secondary" | "danger" | "success";
    IconLeft?: React.ComponentType<any>;
    IconRight?: Boolean;
    className?: string;
}


declare interface InputFieldProps extends TextInputProps {
    label?: string;
    icon?: any;
    secureTextEntry?: boolean;
    labelStyle?: string;
    containerStyle?: string;
    inputStyle?: string;
    iconStyle?: string;
    className?: string;
    required?: boolean;
    items?: { id: number; name: string; code: string; nationality: string }[];
    itemSelect?: string[];
    type?:
        | "text"
        | "dropdown"
        | "textarea"
        | "phone"
        | "date"
        | "radio"
        | "select";
    options?: { label: string; value: boolean | number }[];
    selectedValue?: string | number;
    value?: any;
    fieldKey?: string;
    focusedField?: null
}
